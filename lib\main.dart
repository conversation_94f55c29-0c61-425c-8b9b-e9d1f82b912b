import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:webview_flutter/webview_flutter.dart';
import 'package:flutter_native_splash/flutter_native_splash.dart';

void main() {
  WidgetsBinding widgetsBinding = WidgetsFlutterBinding.ensureInitialized();
  FlutterNativeSplash.preserve(widgetsBinding: widgetsBinding);
  runApp(const MyApp());
}

class MyApp extends StatelessWidget {
  const MyApp({super.key});

  @override
  Widget build(BuildContext context) {
    return const MaterialApp(
      title: 'Winplus Pharma',
      home: WebViewApp(),
    );
  }
}

class WebViewApp extends StatefulWidget {
  const WebViewApp({super.key});

  @override
  _WebViewAppState createState() => _WebViewAppState();
}

class _WebViewAppState extends State<WebViewApp> with TickerProviderStateMixin {
  late WebViewController _controller;
  String _currentUrl = 'https://winpharmplus.ma';
  bool _isExternalSite = false;
  late AnimationController _animationController;
  late Animation<double> _scaleAnimation;
  late Animation<Offset> _slideAnimation;
  bool _splashRemoved = false;

  // Define your main domain
  static const String mainDomain = 'https://winpharmplus.ma';

  @override
  void initState() {
    super.initState();

    // Restore system navigation bar (show default Android navigation)
    SystemChrome.setEnabledSystemUIMode(
      SystemUiMode.edgeToEdge,
      overlays: [SystemUiOverlay.top, SystemUiOverlay.bottom], //
    );

    // Initialize animations
    _animationController = AnimationController(
      duration: const Duration(milliseconds: 300),
      vsync: this,
    );

    _scaleAnimation = Tween<double>(
      begin: 0.0,
      end: 1.0,
    ).animate(CurvedAnimation(
      parent: _animationController,
      curve: Curves.elasticOut,
    ));

    _slideAnimation = Tween<Offset>(
      begin: const Offset(0, 1),
      end: Offset.zero,
    ).animate(CurvedAnimation(
      parent: _animationController,
      curve: Curves.easeOutBack,
    ));

    _controller = WebViewController()
      ..setJavaScriptMode(JavaScriptMode.unrestricted)
      ..setNavigationDelegate(
        NavigationDelegate(
          onUrlChange: (UrlChange urlChange) {
            _handleUrlChange(urlChange.url ?? '');
          },
          onPageFinished: (String url) {
            // Add any actions to perform when the page finishes loading
            // Inject JavaScript to set localStorage variable
            _controller.runJavaScript('localStorage.setItem("fromFlutterWebView", "true");');
            _handleUrlChange(url);

            // Remove splash screen after the first page loads
            FlutterNativeSplash.remove();
          },
          onWebResourceError: (WebResourceError error) {
            print('WebView Error: ${error.description}');
            // You can show a custom error page or retry logic here
          },
          onNavigationRequest: (NavigationRequest request) {
            // Allow all navigation requests
            return NavigationDecision.navigate;
          },
        ),
      )
      ..setUserAgent("fromFlutterWebView")
      ..loadRequest(Uri.parse(mainDomain));
  }

  @override
  void dispose() {
    _animationController.dispose();
    super.dispose();
  }

  void _handleUrlChange(String url) {
    setState(() {
      _currentUrl = url;
      // Check if URL starts with main domain (both HTTP and HTTPS versions)
      bool isMainDomain = url.startsWith(mainDomain) ||
                         url.startsWith(mainDomain.replaceFirst('http://', 'https://'));

      // Show button if: external site OR contains 'assets' (even on main domain)
      bool containsAssets = url.toLowerCase().contains('assets');
      _isExternalSite = !isMainDomain || containsAssets;
    });

    if (_isExternalSite) {
      _animationController.forward();
    } else {
      _animationController.reverse();
    }

    // Debug print to see current URL (optional)
    print('Current URL: $_currentUrl, Contains Assets: ${url.toLowerCase().contains('assets')}, Is External: $_isExternalSite');
  }

  void _goBackToMainSite() {
    _controller.loadRequest(Uri.parse(mainDomain));
  }

  void _goBack() async {
    if (await _controller.canGoBack()) {
      await _controller.goBack();
    } else {
      _goBackToMainSite();
    }
  }

  void _goForward() async {
    if (await _controller.canGoForward()) {
      await _controller.goForward();
    }
  }

  void _refresh() {
    _controller.reload();
  }

  void _showMenu() {
    // Placeholder for menu functionality
    print('Menu tapped');
  }

Widget _buildModernNavButton({
    required IconData icon,
    required VoidCallback onTap,
    required String tooltip,
    bool isHighlighted = false,
  }) {
    return Tooltip(
      message: tooltip,
      child: GestureDetector(
        onTap: onTap,
        child: TweenAnimationBuilder<double>(
          duration: const Duration(milliseconds: 200),
          tween: Tween(begin: 1.0, end: 1.0),
          builder: (context, scale, child) {
            return Transform.scale(
              scale: scale,
              child: Container(
                width: 48,
                height: 48,
                decoration: BoxDecoration(
                  borderRadius: BorderRadius.circular(16),
                  gradient: isHighlighted
                      ? const LinearGradient(
                          colors: [
                            Color(0xFF667eea),
                            Color(0xFF764ba2),
                          ],
                          begin: Alignment.topLeft,
                          end: Alignment.bottomRight,
                        )
                      : LinearGradient(
                          colors: [
                            Color(0xFF2a2a2a),
                            Color(0xFF1a1a1a),
                          ],
                          begin: Alignment.topLeft,
                          end: Alignment.bottomRight,
                        ),
                  boxShadow: [
                    BoxShadow(
                      color: Colors.black.withOpacity(0.2),
                      blurRadius: 4,
                      offset: const Offset(0, 2),
                    ),
                  ],
                ),
                child: Icon(
                  icon,
                  color: Colors.white.withOpacity(0.9),
                  size: 22,
                ),
              ),
            );
          },
        ),
      ),
    );
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      body: SafeArea(
        child: Stack(
          children: [
            // WebView - Full screen
            WebViewWidget(controller: _controller),

            // Floating Back Button - ONLY show when on external site
            if (_isExternalSite)
              Positioned(
                top: 16,
                left: 16,
                child: SlideTransition(
                  position: _slideAnimation,
                  child: ScaleTransition(
                    scale: _scaleAnimation,
                    child: Container(
                      decoration: BoxDecoration(
                        gradient: const LinearGradient(
                          colors: [Color(0xFF667eea), Color(0xFF764ba2)],
                          begin: Alignment.topLeft,
                          end: Alignment.bottomRight,
                        ),
                        borderRadius: BorderRadius.circular(25),
                        boxShadow: [
                          BoxShadow(
                            color: Colors.black.withOpacity(0.2),
                            blurRadius: 6,
                            offset: const Offset(0, 2),
                          ),
                        ],
                      ),
                      child: Material(
                        color: Colors.transparent,
                        child: InkWell(
                          borderRadius: BorderRadius.circular(25),
                          onTap: _goBack,
                          child: Container(
                            padding: const EdgeInsets.symmetric(
                              horizontal: 16,
                              vertical: 12,
                            ),
                            child: const Row(
                              mainAxisSize: MainAxisSize.min,
                              children: [
                                Icon(
                                  Icons.arrow_back_ios,
                                  color: Colors.white,
                                  size: 20,
                                ),
                                SizedBox(width: 4),
                                Text(
                                  'Retour',
                                  style: TextStyle(
                                    color: Colors.white,
                                    fontSize: 16,
                                    fontWeight: FontWeight.w600,
                                  ),
                                ),
                              ],
                            ),
                          ),
                        ),
                      ),
                    ),
                  ),
                ),
              ),


          ],
        ),
      ),
      // // Beautiful Bottom Navigation Bar
      // bottomNavigationBar: Container(
      //   height: 80,
      //   decoration: BoxDecoration(
      //     gradient: const LinearGradient(
      //       colors: [
      //         Color(0xFF2a2a2a),
      //         Color(0xFF1a1a1a),
      //       ],
      //       begin: Alignment.topCenter,
      //       end: Alignment.bottomCenter,
      //     ),
      //     boxShadow: [
      //       BoxShadow(
      //         color: Colors.black.withOpacity(0.3),
      //         blurRadius: 8,
      //         offset: const Offset(0, -2),
      //       ),
      //     ],
      //   ),
      //   child: SafeArea(
      //     child: Padding(
      //       padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
      //       child: Row(
      //         mainAxisAlignment: MainAxisAlignment.spaceEvenly,
      //         children: [
      //           _buildModernNavButton(
      //             icon: Icons.arrow_back_ios_new,
      //             onTap: _goBack,
      //             tooltip: 'Retour',
      //           ),
      //           _buildModernNavButton(
      //             icon: Icons.home_filled,
      //             onTap: _goBackToMainSite,
      //             tooltip: 'Accueil',
      //             isHighlighted: true,
      //           ),
      //           _buildModernNavButton(
      //             icon: Icons.arrow_forward_ios,
      //             onTap: _goForward,
      //             tooltip: 'Suivant',
      //           ),
      //           _buildModernNavButton(
      //             icon: Icons.refresh_rounded,
      //             onTap: _refresh,
      //             tooltip: 'Actualiser',
      //           ),
      //         ],
      //       ),
      //     ),
      //   ),
      // ),
    );
  }
}
