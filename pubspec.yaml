name: webview_app
description: "A new Flutter project."
version: 1.0.0+1

environment:
  sdk: '>=3.4.3 <4.0.0'

dependencies:
  flutter:
    sdk: flutter
  webview_flutter: ^4.8.0
  cupertino_icons: ^1.0.6
  flutter_native_splash: ^2.4.0  # Add this to dependencies

dev_dependencies:
  flutter_test:
    sdk: flutter
  flutter_lints: ^3.0.0
  flutter_launcher_icons: ^0.13.1

flutter:
  uses-material-design: true

  assets:
    - assets/icon/app_icon.png
    - assets/splash/splash_icon.jpg

flutter_launcher_icons:
  android: true
  ios: true
  image_path: "assets/icon/app_icon.png"
  min_sdk_android: 21

flutter_native_splash:
  color: "#ffffff"
  image: assets/splash/splash_icon.jpg
  android: true
  ios: true
  fullscreen: true

  # Android 12+ specific configuration
  android_12:
    color: "#ffffff"
    image: assets/splash/splash_icon.jpg
